# -*- coding: utf-8 -*-
"""
测试大文件重命名对话框功能
"""
import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径，以便导入解压缩GUI模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 解压缩GUI import UnzipGUI

def test_dialog():
    """测试大文件处理对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    app = UnzipGUI(root)

    # 模拟一个大文件
    test_filename = "test_large_file.unknown"
    test_size = "500.0MB"
    test_ext = ".unknown"

    print("正在显示大文件处理对话框...")
    print("请测试以下功能：")
    print("1. 选择'重命名为 .7z'，应该显示确认对话框")
    print("2. 选择'重命名为 .zip'，应该显示确认对话框")
    print("3. 选择'手动重命名文件'，输入新文件名，应该显示确认对话框")
    print("4. 选择'跳过此文件'，应该直接返回结果")
    print()

    result = app.show_large_file_dialog(test_filename, test_size, test_ext)

    print(f"用户选择结果: {result}")

    if result == "rename_7z":
        print("✓ 用户确认重命名为 .7z")
    elif result == "rename_zip":
        print("✓ 用户确认重命名为 .zip")
    elif result and result.startswith("manual:"):
        new_name = result[7:]
        print(f"✓ 用户确认手动重命名为: {new_name}")
    elif result == "skip":
        print("✓ 用户选择跳过")
    else:
        print("✗ 用户取消操作或关闭对话框")

    root.destroy()

if __name__ == "__main__":
    test_dialog()
