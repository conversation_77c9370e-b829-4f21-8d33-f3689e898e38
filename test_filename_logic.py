# -*- coding: utf-8 -*-
"""
测试文件名处理逻辑
"""
import os
import re

def has_triple_extension(filepath):
    """检查是否为三段式扩展名"""
    basename = os.path.basename(filepath)
    parts = basename.split('.')
    print(f"文件名: {basename}")
    print(f"分割后的部分: {parts}")
    print(f"部分数量: {len(parts)}")
    
    # 如果有4个或更多部分（文件名 + 3个扩展名），且最后三个都是扩展名格式
    if len(parts) >= 4:
        # 检查最后三个部分是否都像扩展名（长度2-4个字符，且不全是数字）
        last_three = parts[-3:]
        print(f"最后三个部分: {last_three}")
        for i, part in enumerate(last_three):
            print(f"  部分{i+1}: '{part}', 长度: {len(part)}")
            if len(part) < 2 or len(part) > 4:
                print(f"    -> 长度不符合要求")
                return False
        print("  -> 所有部分长度都符合要求")
        return True
    else:
        print("  -> 部分数量不足4个")
        return False

def test_filename(filename):
    print(f"\n{'='*50}")
    print(f"测试文件名: {filename}")
    print(f"{'='*50}")
    
    # 模拟 os.path.splitext
    base_filename, ext = os.path.splitext(filename)
    print(f"os.path.splitext 结果:")
    print(f"  filename: '{base_filename}'")
    print(f"  ext: '{ext}'")
    
    # 检查是否为三段式扩展名
    is_triple = has_triple_extension(filename)
    print(f"\n三段式扩展名检测结果: {is_triple}")
    
    # 检查扩展名是否在已知列表中
    known_extensions = {
        # 压缩文件
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
        # 视频文件
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
        # 音频文件
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
        # 图片文件
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
        # 文档文件
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
        # 程序文件
        '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
        # 其他常见文件
        '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
    }
    
    ext_in_known = ext.lower() in known_extensions
    print(f"扩展名 '{ext}' 在已知列表中: {ext_in_known}")
    
    # 最终判断条件
    condition = (not ext or ext.lower() not in known_extensions or is_triple)
    print(f"\n最终判断条件:")
    print(f"  not ext: {not ext}")
    print(f"  ext.lower() not in known_extensions: {ext.lower() not in known_extensions}")
    print(f"  is_triple_extension: {is_triple}")
    print(f"  最终结果 (会被视为未知扩展名): {condition}")

if __name__ == "__main__":
    # 测试几个文件名
    test_filenames = [
        "L-叛逆.7z.005.avi",
        "L-MOONN.7z.003.zip",
        "L-MOONN.7z.003",
        "L-MOONN.7z",
        "normal_file.avi",
        "test.pdf.backup.zip"
    ]
    
    for filename in test_filenames:
        test_filename(filename)
